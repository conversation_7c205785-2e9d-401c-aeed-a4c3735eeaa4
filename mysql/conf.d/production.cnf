# MySQL 8.0 生产环境配置
# 适用于MVP产品的基本生产配置，确保数据安全和基本性能

[mysqld]
# === 基本设置 ===
# 服务器ID（用于复制，如果将来需要）
server-id = 1

# === 字符集设置 ===
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init-connect = 'SET NAMES utf8mb4'

# === 时区设置 ===
default-time-zone = '+08:00'

# === 连接设置 ===
max_connections = 200
max_connect_errors = 100000
max_allowed_packet = 64M
wait_timeout = 28800
interactive_timeout = 28800

# === InnoDB 存储引擎设置 ===
# 缓冲池大小（建议为可用内存的70-80%，这里设置为256M适合小型服务器）
innodb_buffer_pool_size = 256M

# 日志文件大小
innodb_log_file_size = 64M
innodb_log_files_in_group = 2

# 数据持久化设置（确保数据不丢失）
innodb_flush_log_at_trx_commit = 1  # 每次事务提交都写入磁盘
innodb_flush_method = O_DIRECT

# 文件格式
innodb_file_format = Barracuda
innodb_file_per_table = 1

# === 二进制日志设置（用于备份和恢复）===
log-bin = mysql-bin
binlog-format = ROW
sync_binlog = 1  # 每次事务提交都同步binlog到磁盘
expire_logs_days = 7  # 保留7天的binlog
max_binlog_size = 100M

# === 慢查询日志 ===
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2  # 记录执行时间超过2秒的查询

# === 错误日志 ===
log_error = /var/log/mysql/error.log

# === 一般查询日志（生产环境通常关闭以节省性能）===
general_log = 0

# === 安全设置 ===
# 禁用本地文件加载
local_infile = 0

# === 性能优化 ===
# 查询缓存（MySQL 8.0已移除，这里注释掉）
# query_cache_type = 1
# query_cache_size = 32M

# 表缓存
table_open_cache = 2000
table_definition_cache = 1400

# 临时表设置
tmp_table_size = 32M
max_heap_table_size = 32M

# 排序缓冲区
sort_buffer_size = 2M
read_buffer_size = 128K
read_rnd_buffer_size = 256K

# 连接缓冲区
join_buffer_size = 128K

# === 复制设置（为将来扩展准备）===
# 如果将来需要主从复制，可以启用以下设置
# log_slave_updates = 1
# relay_log = mysql-relay-bin

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
