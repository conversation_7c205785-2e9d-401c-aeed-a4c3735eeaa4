# MySQL 8.0 生产环境配置
# 适用于MVP产品的基本生产配置，确保数据安全和基本性能

[mysqld]
# === 基本设置 ===
server-id = 1

# === 字符集设置 ===
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# === 时区设置 ===
default-time-zone = '+08:00'

# === 连接设置 ===
max_connections = 200
max_allowed_packet = 64M

# === InnoDB 存储引擎设置 ===
innodb_buffer_pool_size = 256M
innodb_flush_log_at_trx_commit = 1

# === 二进制日志设置 ===
log-bin = mysql-bin
binlog-format = ROW
sync_binlog = 1
binlog_expire_logs_seconds = 604800
max_binlog_size = 100M

# === 日志设置 ===
slow_query_log = 1
long_query_time = 2

# === 安全设置 ===
local_infile = 0

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
